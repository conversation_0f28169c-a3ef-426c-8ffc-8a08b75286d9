/* CSS Variables for Theme System */
:root {
    /* Dark Theme (Default) */
    --bg-primary: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    --bg-secondary: rgba(255, 255, 255, 0.05);
    --bg-tertiary: rgba(255, 255, 255, 0.1);
    --text-primary: #ffffff;
    --text-secondary: #b8b8b8;
    --text-muted: #888888;
    --accent-primary: #8b5cf6;
    --accent-secondary: #a855f7;
    --accent-gradient: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
    --border-color: rgba(255, 255, 255, 0.1);
    --shadow-light: rgba(139, 92, 246, 0.1);
    --shadow-dark: rgba(0, 0, 0, 0.3);
    --window-bg: rgba(26, 26, 46, 0.95);
    --sidebar-bg: rgba(255, 255, 255, 0.03);
    --terminal-bg: #0d1117;
    --terminal-text: #58a6ff;
    --success-color: #22c55e;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
}

[data-theme="light"] {
    --bg-primary: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
    --bg-secondary: rgba(0, 0, 0, 0.05);
    --bg-tertiary: rgba(0, 0, 0, 0.1);
    --text-primary: #1e293b;
    --text-secondary: #475569;
    --text-muted: #64748b;
    --accent-primary: #8b5cf6;
    --accent-secondary: #a855f7;
    --accent-gradient: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
    --border-color: rgba(0, 0, 0, 0.1);
    --shadow-light: rgba(139, 92, 246, 0.1);
    --shadow-dark: rgba(0, 0, 0, 0.1);
    --window-bg: rgba(248, 250, 252, 0.95);
    --sidebar-bg: rgba(0, 0, 0, 0.03);
    --terminal-bg: #f8fafc;
    --terminal-text: #0969da;
    --success-color: #22c55e;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow: hidden;
    transition: all 0.3s ease;
}

/* Portfolio Container */
.portfolio-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--window-bg);
    backdrop-filter: blur(20px);
    border-radius: 12px;
    margin: 20px;
    box-shadow: 0 20px 40px var(--shadow-dark);
    overflow: hidden;
    transition: all 0.3s ease;
}

/* Window Header */
.window-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 20px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
}

.window-controls {
    display: flex;
    gap: 8px;
}

.control {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
}

.control.close {
    background: #ff5f57;
}

.control.minimize {
    background: #ffbd2e;
}

.control.maximize {
    background: #28ca42;
}

.control:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.window-title {
    font-weight: 500;
    font-size: 14px;
    color: var(--text-secondary);
}

.theme-toggle-container {
    display: flex;
    align-items: center;
}

.theme-toggle {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 8px 12px;
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.theme-toggle:hover {
    background: var(--accent-primary);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px var(--shadow-light);
}

/* Main Content Layout */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Sidebar Navigation */
.sidebar {
    width: 240px;
    background: var(--sidebar-bg);
    border-right: 1px solid var(--border-color);
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
}

.nav-items {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 0 16px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    border-radius: 8px;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 14px;
}

.nav-item:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
    transform: translateX(4px);
}

.nav-item.active {
    background: var(--accent-gradient);
    color: white;
    box-shadow: 0 4px 12px var(--shadow-light);
}

.nav-item i {
    width: 16px;
    text-align: center;
    font-size: 16px;
}

/* Content Area */
.content-area {
    flex: 1;
    overflow-y: auto;
    padding: 40px;
    position: relative;
}

.content-section {
    display: none;
    animation: fadeIn 0.5s ease;
}

.content-section.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.section-content {
    max-width: 800px;
    margin: 0 auto;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Hero Section */
.hero-section {
    text-align: center;
    padding: 60px 0;
}

.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: var(--bg-secondary);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    border: 1px solid var(--border-color);
}

.status-dot {
    width: 8px;
    height: 8px;
    background: var(--success-color);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
}

.highlight {
    color: var(--accent-primary);
}

.gradient-text {
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto 2.5rem;
    line-height: 1.7;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Buttons */
.btn-primary, .btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    font-size: 16px;
}

.btn-primary {
    background: var(--accent-gradient);
    color: white;
    box-shadow: 0 4px 12px var(--shadow-light);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px var(--shadow-light);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-tertiary);
    transform: translateY(-2px);
}

/* Scrollbar Styling */
.content-area::-webkit-scrollbar {
    width: 6px;
}

.content-area::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

.content-area::-webkit-scrollbar-thumb {
    background: var(--accent-primary);
    border-radius: 3px;
}

.content-area::-webkit-scrollbar-thumb:hover {
    background: var(--accent-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .portfolio-container {
        margin: 10px;
        border-radius: 8px;
    }
    
    .sidebar {
        width: 200px;
    }
    
    .content-area {
        padding: 20px;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
}

/* Projects Grid */
.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 24px;
    margin-top: 2rem;
}

.project-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 24px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.project-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 24px var(--shadow-dark);
    border-color: var(--accent-primary);
}

.project-card.featured {
    border: 2px solid var(--accent-primary);
    background: linear-gradient(135deg, var(--bg-secondary) 0%, rgba(139, 92, 246, 0.05) 100%);
}

.project-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.project-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.project-link {
    color: var(--accent-primary);
    font-size: 18px;
    transition: all 0.3s ease;
}

.project-link:hover {
    transform: scale(1.2);
    color: var(--accent-secondary);
}

.project-description {
    color: var(--text-secondary);
    margin-bottom: 16px;
    line-height: 1.6;
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tech-tag {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid var(--border-color);
}

/* Blog Grid */
.blog-grid {
    display: grid;
    gap: 24px;
    margin-top: 2rem;
}

.blog-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 24px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.blog-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px var(--shadow-dark);
    border-color: var(--accent-primary);
}

.blog-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 14px;
    color: var(--text-muted);
}

.blog-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 12px;
    line-height: 1.3;
}

.blog-excerpt {
    color: var(--text-secondary);
    margin-bottom: 16px;
    line-height: 1.6;
}

.blog-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
}

.blog-tag {
    background: var(--accent-primary);
    color: white;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
}

.blog-read-more {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: var(--accent-primary);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.blog-read-more:hover {
    color: var(--accent-secondary);
    transform: translateX(4px);
}

/* Contact Grid */
.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-top: 2rem;
}

.contact-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 24px;
    text-align: center;
    transition: all 0.3s ease;
}

.contact-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px var(--shadow-dark);
}

.contact-icon {
    width: 48px;
    height: 48px;
    background: var(--accent-gradient);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    font-size: 20px;
    color: white;
}

.contact-card h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.contact-card p {
    color: var(--text-secondary);
    margin-bottom: 16px;
}

.copy-btn, .contact-link {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.copy-btn:hover, .contact-link:hover {
    background: var(--accent-primary);
    color: white;
    transform: translateY(-1px);
}

.status-badge {
    background: var(--success-color);
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

/* Social Grid */
.socials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin-top: 2rem;
}

.social-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 24px;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    display: block;
}

.social-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 24px var(--shadow-dark);
    border-color: var(--accent-primary);
}

.social-icon {
    width: 48px;
    height: 48px;
    background: var(--accent-gradient);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    font-size: 20px;
    color: white;
}

.social-card h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.social-card p {
    color: var(--text-secondary);
    line-height: 1.5;
}

/* Terminal Styles */
.terminal-container {
    background: var(--terminal-bg);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 24px var(--shadow-dark);
    border: 1px solid var(--border-color);
    max-width: 800px;
    margin: 0 auto;
}

.terminal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid var(--border-color);
}

.terminal-controls {
    display: flex;
    gap: 8px;
}

.terminal-title {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    color: var(--text-secondary);
}

.terminal-body {
    padding: 20px;
    min-height: 400px;
    max-height: 500px;
    overflow-y: auto;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
}

.terminal-output {
    margin-bottom: 16px;
}

.terminal-line {
    margin-bottom: 4px;
    color: var(--terminal-text);
}

.terminal-line.command {
    color: var(--text-primary);
}

.terminal-line.error {
    color: var(--error-color);
}

.terminal-line.success {
    color: var(--success-color);
}

.terminal-line.info {
    color: var(--terminal-text);
}

.terminal-line.warning {
    color: var(--warning-color);
}

.terminal-input-line {
    display: flex;
    align-items: center;
    position: sticky;
    bottom: 0;
    background: var(--terminal-bg);
    padding-top: 8px;
}

.terminal-prompt {
    color: var(--success-color);
    margin-right: 8px;
    font-weight: 600;
}

.terminal-input {
    flex: 1;
    background: transparent;
    border: none;
    outline: none;
    color: var(--terminal-text);
    font-family: inherit;
    font-size: inherit;
    caret-color: var(--accent-primary);
}

.terminal-input::placeholder {
    color: var(--text-muted);
}

/* Terminal scrollbar */
.terminal-body::-webkit-scrollbar {
    width: 6px;
}

.terminal-body::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
}

.terminal-body::-webkit-scrollbar-thumb {
    background: var(--accent-primary);
    border-radius: 3px;
}

.terminal-body::-webkit-scrollbar-thumb:hover {
    background: var(--accent-secondary);
}

@media (max-width: 640px) {
    .main-content {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        padding: 10px 0;
    }

    .nav-items {
        flex-direction: row;
        overflow-x: auto;
        padding: 0 16px;
    }

    .nav-item {
        white-space: nowrap;
        min-width: fit-content;
    }

    .content-area {
        padding: 20px 16px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .projects-grid, .contact-grid, .socials-grid {
        grid-template-columns: 1fr;
    }
}
