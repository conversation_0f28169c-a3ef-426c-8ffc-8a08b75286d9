// Interactive Terminal Implementation for Geofrey28 Portfolio

class Terminal {
    constructor() {
        this.commands = {
            help: this.showHelp.bind(this),
            '--help': this.showHelp.bind(this),
            about: this.showAbout.bind(this),
            skills: this.showSkills.bind(this),
            projects: this.showProjects.bind(this),
            contact: this.showContact.bind(this),
            resume: this.showResume.bind(this),
            whoami: this.showWhoami.bind(this),
            clear: this.clearTerminal.bind(this),
            social: this.showSocial.bind(this),
            certifications: this.showCertifications.bind(this),
            experience: this.showExperience.bind(this),
            education: this.showEducation.bind(this),
            ls: this.listDirectory.bind(this),
            pwd: this.showCurrentDirectory.bind(this),
            cat: this.showFile.bind(this),
            echo: this.echo.bind(this),
            date: this.showDate.bind(this),
            uptime: this.showUptime.bind(this)
        };
        
        this.currentDirectory = '~';
        this.startTime = Date.now();
        this.commandHistory = [];
        this.historyIndex = -1;
        
        this.init();
    }

    init() {
        this.terminalInput = document.getElementById('terminalInput');
        this.terminalOutput = document.getElementById('terminalOutput');
        
        if (this.terminalInput) {
            this.terminalInput.addEventListener('keydown', this.handleKeyDown.bind(this));
            this.terminalInput.focus();
        }

        // Focus terminal input when terminal section is clicked
        const terminal = document.getElementById('terminal');
        if (terminal) {
            terminal.addEventListener('click', () => {
                this.terminalInput.focus();
            });
        }
    }

    handleKeyDown(event) {
        if (event.key === 'Enter') {
            const command = this.terminalInput.value.trim();
            if (command) {
                this.executeCommand(command);
                this.commandHistory.push(command);
                this.historyIndex = this.commandHistory.length;
            }
            this.terminalInput.value = '';
        } else if (event.key === 'ArrowUp') {
            event.preventDefault();
            if (this.historyIndex > 0) {
                this.historyIndex--;
                this.terminalInput.value = this.commandHistory[this.historyIndex];
            }
        } else if (event.key === 'ArrowDown') {
            event.preventDefault();
            if (this.historyIndex < this.commandHistory.length - 1) {
                this.historyIndex++;
                this.terminalInput.value = this.commandHistory[this.historyIndex];
            } else {
                this.historyIndex = this.commandHistory.length;
                this.terminalInput.value = '';
            }
        } else if (event.key === 'Tab') {
            event.preventDefault();
            this.autoComplete();
        }
    }

    executeCommand(input) {
        const [command, ...args] = input.split(' ');
        
        // Add command to output
        this.addOutput(`geofrey@portfolio:${this.currentDirectory}$ ${input}`, 'command');
        
        if (this.commands[command.toLowerCase()]) {
            this.commands[command.toLowerCase()](args);
        } else {
            this.addOutput(`Command not found: ${command}. Type 'help' for available commands.`, 'error');
        }
        
        this.scrollToBottom();
    }

    addOutput(text, type = 'normal') {
        const line = document.createElement('div');
        line.className = `terminal-line ${type}`;
        
        if (type === 'command') {
            line.innerHTML = `<span class="terminal-prompt">geofrey@portfolio:${this.currentDirectory}$ </span>${text.replace(/^geofrey@portfolio:.*\$ /, '')}`;
        } else {
            line.textContent = text;
        }
        
        this.terminalOutput.appendChild(line);
    }

    scrollToBottom() {
        this.terminalOutput.scrollTop = this.terminalOutput.scrollHeight;
    }

    autoComplete() {
        const input = this.terminalInput.value;
        const matches = Object.keys(this.commands).filter(cmd => cmd.startsWith(input));
        
        if (matches.length === 1) {
            this.terminalInput.value = matches[0];
        } else if (matches.length > 1) {
            this.addOutput(`Available commands: ${matches.join(', ')}`, 'info');
            this.scrollToBottom();
        }
    }

    // Command implementations
    showHelp() {
        const helpText = `
Available Commands:
==================
help, --help     Show this help message
about            Display personal information and bio
skills           Show technical skills and proficiencies
projects         List all projects with descriptions
contact          Show contact information
resume           Display/download resume information
whoami           Brief introduction
clear            Clear terminal screen
social           Display social media links
certifications   Show Oracle certification details
experience       Show work experience
education        Show educational background
ls               List directory contents
pwd              Show current directory
cat <file>       Display file contents
echo <text>      Display text
date             Show current date and time
uptime           Show system uptime

Navigation:
- Use Tab for auto-completion
- Use ↑/↓ arrows for command history
- Type any command and press Enter
        `;
        this.addOutput(helpText.trim(), 'info');
    }

    showAbout() {
        const aboutText = `
Name: Geofrey
Title: Full-Stack Developer & AI Engineer
Status: Available for hire

Bio:
I am a Computer Science student deeply immersed in the realms of AI, 
machine learning, software development, and networking. With a fervent 
passion for innovation, I've honed my skills through engaging projects 
that showcase my ability to translate complex ideas into practical solutions.

Whether crafting elegant code for software development or designing secure 
network architectures, I bring a dynamic approach to technology. Eager to 
contribute my enthusiasm and expertise to a forward-thinking team, I'm 
excited about the prospect of driving impactful change in the tech landscape.
        `;
        this.addOutput(aboutText.trim(), 'info');
    }

    showSkills() {
        const skillsText = `
Technical Skills:
================

Programming Languages:
• JavaScript (ES6+, Node.js)
• Python (Django, Flask, FastAPI)
• TypeScript
• HTML5 & CSS3
• SQL

Frontend Technologies:
• React.js & Next.js
• Vue.js
• Tailwind CSS
• Responsive Design
• Progressive Web Apps

Backend Technologies:
• Node.js & Express
• Python Web Frameworks
• RESTful APIs
• GraphQL
• Microservices Architecture

AI/ML Technologies:
• Machine Learning
• Computer Vision
• Natural Language Processing
• TensorFlow & PyTorch
• Local LLM Deployment
• AI Agent Development

Cloud & DevOps:
• Oracle Cloud Infrastructure
• Docker & Containerization
• CI/CD Pipelines
• Git Version Control

Databases:
• MongoDB
• PostgreSQL
• MySQL
• Redis
        `;
        this.addOutput(skillsText.trim(), 'success');
    }

    showProjects() {
        const projectsText = `
Featured Projects:
=================

1. DataLearn-Hub8
   → Comprehensive data science learning platform
   → Tech: React, Node.js, Python, Machine Learning
   → URL: https://datalearnhub8.vercel.app

2. Job8earch
   → AI-Powered Job Search Platform
   → Tech: React, AI/ML, Node.js, MongoDB
   → URL: https://job8earch.vercel.app

3. 200Model8-Chat
   → Free AI Chat Interface with multiple AI models
   → Tech: React, AI APIs, WebSocket, Node.js
   → URL: https://200-model8-chat.vercel.app

4. LAX - Local AI eXecution
   → Local AI model deployment tool
   → Tech: Python, Docker, AI/ML, CLI
   → URL: https://github.com/Jeff9497/LAX

Other Notable Projects:
• Ollama8Web - Modern web interface for Ollama
• EvSentry8 - AI-powered tomato leaf disease detection
• FarMVi8ioN - Farm Vision AI for agriculture
• ChatBuddy28 - WhatsApp chat automation
• Whatsapify - WhatsApp bot powered by ChatGPT
        `;
        this.addOutput(projectsText.trim(), 'success');
    }

    showContact() {
        const contactText = `
Contact Information:
===================
Email: <EMAIL>
GitHub: https://github.com/Jeff9497
Location: Available Worldwide (Remote Ready)
Status: Available for hire

Feel free to reach out for:
• Full-stack development projects
• AI/ML consulting and development
• Open source collaboration
• Technical discussions
        `;
        this.addOutput(contactText.trim(), 'info');
    }

    showResume() {
        const resumeText = `
Resume Summary:
==============
Name: Geofrey
Title: Full-Stack Developer & AI Engineer
Education: Computer Science Student
Specializations: AI/ML, Web Development, Networking

Key Achievements:
• Developed multiple successful web applications
• Specialized in AI agent development and local LLM deployment
• Oracle certified in Cloud Infrastructure and AI Foundations
• Active open source contributor

To download full resume: [Feature coming soon]
        `;
        this.addOutput(resumeText.trim(), 'info');
    }

    showWhoami() {
        this.addOutput("Geofrey - Full-Stack Developer & AI Engineer passionate about innovation and technology", 'success');
    }

    clearTerminal() {
        this.terminalOutput.innerHTML = `
            <div class="terminal-line">Welcome to Geofrey's Interactive Terminal</div>
            <div class="terminal-line">Type 'help' to see available commands</div>
        `;
    }

    showSocial() {
        const socialText = `
Social Media Links:
==================
GitHub: https://github.com/Jeff9497
LinkedIn: [Connect with me professionally]
Twitter: [Follow for tech insights]
Dev.to: [Read my technical articles]

Connect with me to discuss:
• AI and Machine Learning
• Full-stack development
• Open source projects
• Technology trends
        `;
        this.addOutput(socialText.trim(), 'info');
    }

    showCertifications() {
        const certText = `
Certifications:
==============
• Oracle Certified Foundations Associate
• Oracle Cloud Infrastructure 2025 Certified AI Foundations Associate
  (Achieved: July 17, 2025)

These certifications demonstrate expertise in:
• Cloud infrastructure management
• AI and machine learning foundations
• Oracle Cloud services and architecture
• Modern cloud-native development practices
        `;
        this.addOutput(certText.trim(), 'success');
    }

    showExperience() {
        const expText = `
Experience:
==========
Current Focus: Full-Stack Development & AI Engineering
• Developing AI-powered web applications
• Creating local LLM deployment solutions
• Building modern, responsive user interfaces
• Implementing machine learning models in production

Project Experience:
• Led development of multiple successful web applications
• Specialized in AI agent development and automation
• Experience with both frontend and backend technologies
• Active contributor to open source projects
        `;
        this.addOutput(expText.trim(), 'info');
    }

    showEducation() {
        const eduText = `
Education:
=========
Computer Science Student
• Focus on AI, Machine Learning, and Software Development
• Specialized coursework in networking and system architecture
• Hands-on experience with modern development frameworks
• Active in tech communities and continuous learning
        `;
        this.addOutput(eduText.trim(), 'info');
    }

    listDirectory() {
        const files = [
            'about.txt', 'projects/', 'skills.json', 'contact.md', 
            'resume.pdf', 'certifications/', 'social-links.txt'
        ];
        this.addOutput(files.join('  '), 'success');
    }

    showCurrentDirectory() {
        this.addOutput(`/home/<USER>'info');
    }

    showFile(args) {
        const filename = args[0];
        if (!filename) {
            this.addOutput('Usage: cat <filename>', 'error');
            return;
        }

        const files = {
            'about.txt': 'Geofrey - Full-Stack Developer & AI Engineer\nPassionate about innovation and technology.',
            'contact.md': '# Contact\nEmail: <EMAIL>\nGitHub: Jeff9497',
            'skills.json': '{"languages": ["JavaScript", "Python"], "frameworks": ["React", "Node.js"]}'
        };

        if (files[filename]) {
            this.addOutput(files[filename], 'success');
        } else {
            this.addOutput(`cat: ${filename}: No such file or directory`, 'error');
        }
    }

    echo(args) {
        this.addOutput(args.join(' '), 'info');
    }

    showDate() {
        this.addOutput(new Date().toString(), 'info');
    }

    showUptime() {
        const uptime = Math.floor((Date.now() - this.startTime) / 1000);
        const minutes = Math.floor(uptime / 60);
        const seconds = uptime % 60;
        this.addOutput(`Terminal uptime: ${minutes}m ${seconds}s`, 'info');
    }
}

// Initialize terminal when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Wait a bit to ensure the terminal section exists
    setTimeout(() => {
        if (document.getElementById('terminalInput')) {
            new Terminal();
        }
    }, 100);
});

// Export for use in other modules
window.Terminal = Terminal;
