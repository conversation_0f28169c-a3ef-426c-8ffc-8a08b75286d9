// Main JavaScript functionality for Geofrey28 Portfolio

class Portfolio {
    constructor() {
        this.currentTheme = localStorage.getItem('theme') || 'dark';
        this.currentSection = 'about';
        this.init();
    }

    init() {
        this.setupTheme();
        this.setupNavigation();
        this.setupEventListeners();
        this.loadContent();
    }

    setupTheme() {
        document.documentElement.setAttribute('data-theme', this.currentTheme);
        const themeToggle = document.getElementById('themeToggle');
        const icon = themeToggle.querySelector('i');
        
        if (this.currentTheme === 'light') {
            icon.className = 'fas fa-sun';
        } else {
            icon.className = 'fas fa-moon';
        }
    }

    setupNavigation() {
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const section = item.getAttribute('data-section');
                this.navigateToSection(section);
            });
        });
    }

    setupEventListeners() {
        // Theme toggle
        const themeToggle = document.getElementById('themeToggle');
        themeToggle.addEventListener('click', () => this.toggleTheme());

        // Window controls (just for show)
        const controls = document.querySelectorAll('.control');
        controls.forEach(control => {
            control.addEventListener('click', () => {
                control.style.transform = 'scale(0.9)';
                setTimeout(() => {
                    control.style.transform = 'scale(1)';
                }, 100);
            });
        });

        // Smooth scrolling for hero buttons
        window.scrollToSection = (sectionId) => {
            this.navigateToSection(sectionId);
        };
    }

    toggleTheme() {
        this.currentTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
        localStorage.setItem('theme', this.currentTheme);
        this.setupTheme();
    }

    navigateToSection(sectionId) {
        // Update active nav item
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-section="${sectionId}"]`).classList.add('active');

        // Update active section
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(sectionId).classList.add('active');

        this.currentSection = sectionId;
    }

    loadContent() {
        this.loadProjects();
        this.loadBlogPosts();
        this.loadContactInfo();
        this.loadSocialLinks();
    }

    loadProjects() {
        const projectsGrid = document.querySelector('.projects-grid');
        const projects = [
            {
                title: 'DataLearn-Hub8',
                description: 'Comprehensive data science learning platform with interactive tutorials and real-world projects.',
                tech: ['React', 'Node.js', 'Python', 'Machine Learning'],
                link: 'https://datalearnhub8.vercel.app',
                featured: true
            },
            {
                title: 'Job8earch',
                description: 'AI-Powered Job Search Platform built with React, featuring smart job matching and application tracking.',
                tech: ['React', 'AI/ML', 'Node.js', 'MongoDB'],
                link: 'https://job8earch.vercel.app',
                featured: true
            },
            {
                title: '200Model8-Chat',
                description: 'Free AI Chat Interface with multiple AI models, providing users with diverse AI conversation experiences.',
                tech: ['React', 'AI APIs', 'WebSocket', 'Node.js'],
                link: 'https://200-model8-chat.vercel.app',
                featured: true
            },
            {
                title: 'LAX - Local AI eXecution',
                description: 'Local AI model deployment tool for running AI models on your own hardware with privacy and control.',
                tech: ['Python', 'Docker', 'AI/ML', 'CLI'],
                link: 'https://github.com/Jeff9497/LAX',
                featured: true
            },
            {
                title: 'Ollama8Web',
                description: 'Modern web interface for Ollama, making local AI model interaction more user-friendly.',
                tech: ['JavaScript', 'HTML/CSS', 'Ollama API'],
                link: '#',
                featured: false
            },
            {
                title: 'EvSentry8',
                description: 'AI-powered tomato leaf disease detection system using computer vision and machine learning.',
                tech: ['Python', 'TensorFlow', 'Computer Vision', 'Flask'],
                link: '#',
                featured: false
            }
        ];

        projectsGrid.innerHTML = projects.map(project => `
            <div class="project-card ${project.featured ? 'featured' : ''}">
                <div class="project-header">
                    <h3 class="project-title">${project.title}</h3>
                    <a href="${project.link}" target="_blank" class="project-link">
                        <i class="fas fa-external-link-alt"></i>
                    </a>
                </div>
                <p class="project-description">${project.description}</p>
                <div class="project-tech">
                    ${project.tech.map(tech => `<span class="tech-tag">${tech}</span>`).join('')}
                </div>
            </div>
        `).join('');
    }

    loadBlogPosts() {
        const blogGrid = document.querySelector('.blog-grid');
        const posts = [
            {
                title: 'The Rise of AI Agents: Transforming Digital Workflows',
                excerpt: 'Comprehensive exploration of how AI agents are revolutionizing business processes and personal productivity...',
                readTime: '12 min read',
                date: 'Dec 2024',
                tags: ['AI Agents', 'Automation', 'Productivity', 'Machine Learning'],
                slug: 'ai-agents-transforming-workflows'
            },
            {
                title: 'Local LLM Evolution: From GPT-2 to Llama 3.1',
                excerpt: 'Deep dive into the evolution of locally deployable Large Language Models and the democratization of AI...',
                readTime: '10 min read',
                date: 'Nov 2024',
                tags: ['Local LLMs', 'Open Source AI', 'Privacy', 'LLaMA'],
                slug: 'local-llm-evolution'
            },
            {
                title: 'The Future of AI Automation: Beyond Scripts and Workflows',
                excerpt: 'Forward-looking analysis of where AI automation is headed, exploring self-improving systems and AI-human collaboration...',
                readTime: '8 min read',
                date: 'Oct 2024',
                tags: ['AI Automation', 'Future Tech', 'Digital Transformation'],
                slug: 'future-ai-automation'
            }
        ];

        blogGrid.innerHTML = posts.map(post => `
            <article class="blog-card">
                <div class="blog-meta">
                    <span class="blog-date">${post.date}</span>
                    <span class="blog-read-time">${post.readTime}</span>
                </div>
                <h3 class="blog-title">${post.title}</h3>
                <p class="blog-excerpt">${post.excerpt}</p>
                <div class="blog-tags">
                    ${post.tags.map(tag => `<span class="blog-tag">${tag}</span>`).join('')}
                </div>
                <a href="#" class="blog-read-more" onclick="openBlogPost('${post.slug}')">
                    Read More <i class="fas fa-arrow-right"></i>
                </a>
            </article>
        `).join('');
    }

    loadContactInfo() {
        const contactContent = document.querySelector('.contact-content');
        contactContent.innerHTML = `
            <div class="contact-grid">
                <div class="contact-card">
                    <div class="contact-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <h3>Email</h3>
                    <p><EMAIL></p>
                    <button class="copy-btn" onclick="copyToClipboard('<EMAIL>')">
                        <i class="fas fa-copy"></i> Copy
                    </button>
                </div>
                <div class="contact-card">
                    <div class="contact-icon">
                        <i class="fab fa-github"></i>
                    </div>
                    <h3>GitHub</h3>
                    <p>Jeff9497</p>
                    <a href="https://github.com/Jeff9497" target="_blank" class="contact-link">
                        <i class="fas fa-external-link-alt"></i> Visit
                    </a>
                </div>
                <div class="contact-card">
                    <div class="contact-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <h3>Location</h3>
                    <p>Available Worldwide</p>
                    <span class="status-badge">Remote Ready</span>
                </div>
                <div class="contact-card">
                    <div class="contact-icon">
                        <i class="fas fa-certificate"></i>
                    </div>
                    <h3>Certifications</h3>
                    <p>Oracle Certified Foundations Associate</p>
                    <p>Oracle Cloud Infrastructure 2025 AI Foundations</p>
                </div>
            </div>
        `;
    }

    loadSocialLinks() {
        const socialsGrid = document.querySelector('.socials-grid');
        const socials = [
            {
                name: 'GitHub',
                icon: 'fab fa-github',
                url: 'https://github.com/Jeff9497',
                description: 'Check out my code repositories and open source contributions'
            },
            {
                name: 'LinkedIn',
                icon: 'fab fa-linkedin',
                url: '#',
                description: 'Connect with me professionally and see my career journey'
            },
            {
                name: 'Twitter',
                icon: 'fab fa-twitter',
                url: '#',
                description: 'Follow me for tech insights and AI/ML discussions'
            },
            {
                name: 'Dev.to',
                icon: 'fab fa-dev',
                url: '#',
                description: 'Read my technical articles and tutorials'
            }
        ];

        socialsGrid.innerHTML = socials.map(social => `
            <a href="${social.url}" target="_blank" class="social-card">
                <div class="social-icon">
                    <i class="${social.icon}"></i>
                </div>
                <h3>${social.name}</h3>
                <p>${social.description}</p>
            </a>
        `).join('');
    }
}

// Utility functions
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        // Show success feedback
        const btn = event.target.closest('.copy-btn');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
        btn.style.background = 'var(--success-color)';
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.style.background = '';
        }, 2000);
    });
}

function openBlogPost(slug) {
    // For now, just show an alert. In a real implementation, this would open the full blog post
    alert(`Opening blog post: ${slug}\n\nThis would navigate to the full blog post in a real implementation.`);
}

// Initialize portfolio when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new Portfolio();
});

// Export for use in other modules
window.Portfolio = Portfolio;
