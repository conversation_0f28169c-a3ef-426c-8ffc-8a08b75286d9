<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Geo<PERSON> - Full-Stack Developer & AI Engineer</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="portfolio-container">
        <!-- macOS Window Header -->
        <div class="window-header">
            <div class="window-controls">
                <div class="control close"></div>
                <div class="control minimize"></div>
                <div class="control maximize"></div>
            </div>
            <div class="window-title">Geofrey.Portfolio</div>
            <div class="theme-toggle-container">
                <button class="theme-toggle" id="themeToggle">
                    <i class="fas fa-moon"></i>
                </button>
            </div>
        </div>

        <div class="main-content">
            <!-- Sidebar Navigation -->
            <nav class="sidebar">
                <div class="nav-items">
                    <a href="#about" class="nav-item active" data-section="about">
                        <i class="fas fa-user"></i>
                        <span>About</span>
                    </a>
                    <a href="#projects" class="nav-item" data-section="projects">
                        <i class="fas fa-folder"></i>
                        <span>Projects</span>
                    </a>
                    <a href="#blog" class="nav-item" data-section="blog">
                        <i class="fas fa-edit"></i>
                        <span>Blog</span>
                    </a>
                    <a href="#terminal" class="nav-item" data-section="terminal">
                        <i class="fas fa-terminal"></i>
                        <span>Terminal</span>
                    </a>
                    <a href="#contact" class="nav-item" data-section="contact">
                        <i class="fas fa-envelope"></i>
                        <span>Contact</span>
                    </a>
                    <a href="#socials" class="nav-item" data-section="socials">
                        <i class="fas fa-share-alt"></i>
                        <span>Socials</span>
                    </a>
                </div>
            </nav>

            <!-- Content Area -->
            <main class="content-area">
                <!-- About Section -->
                <section id="about" class="content-section active">
                    <div class="section-content">
                        <div class="hero-section">
                            <div class="status-indicator">
                                <span class="status-dot"></span>
                                Available for hire
                            </div>
                            <h1 class="hero-title">
                                Hey, I'm <span class="highlight">Geofrey</span><br>
                                a <span class="gradient-text">Full-Stack Developer & AI Engineer</span>
                            </h1>
                            <p class="hero-description">
                                I am a Computer Science student deeply immersed in the realms of AI, machine learning, 
                                software development, and networking. With a fervent passion for innovation, I've honed 
                                my skills through engaging projects that showcase my ability to translate complex ideas 
                                into practical solutions.
                            </p>
                            <div class="hero-actions">
                                <button class="btn-primary" onclick="scrollToSection('contact')">
                                    <i class="fas fa-envelope"></i>
                                    Get in touch
                                </button>
                                <button class="btn-secondary" onclick="scrollToSection('projects')">
                                    <i class="fas fa-folder"></i>
                                    View projects
                                </button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Projects Section -->
                <section id="projects" class="content-section">
                    <div class="section-content">
                        <h2 class="section-title">Featured Projects</h2>
                        <div class="projects-grid">
                            <!-- Projects will be populated by JavaScript -->
                        </div>
                    </div>
                </section>

                <!-- Blog Section -->
                <section id="blog" class="content-section">
                    <div class="section-content">
                        <h2 class="section-title">Latest Articles</h2>
                        <div class="blog-grid">
                            <!-- Blog posts will be populated by JavaScript -->
                        </div>
                    </div>
                </section>

                <!-- Terminal Section -->
                <section id="terminal" class="content-section">
                    <div class="section-content">
                        <div class="terminal-container">
                            <div class="terminal-header">
                                <div class="terminal-controls">
                                    <div class="control close"></div>
                                    <div class="control minimize"></div>
                                    <div class="control maximize"></div>
                                </div>
                                <div class="terminal-title">geofrey@portfolio:~$</div>
                            </div>
                            <div class="terminal-body" id="terminal">
                                <div class="terminal-output" id="terminalOutput">
                                    <div class="terminal-line">Welcome to Geofrey's Interactive Terminal</div>
                                    <div class="terminal-line">Type 'help' to see available commands</div>
                                </div>
                                <div class="terminal-input-line">
                                    <span class="terminal-prompt">geofrey@portfolio:~$ </span>
                                    <input type="text" class="terminal-input" id="terminalInput" autocomplete="off" spellcheck="false">
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Contact Section -->
                <section id="contact" class="content-section">
                    <div class="section-content">
                        <h2 class="section-title">Get In Touch</h2>
                        <div class="contact-content">
                            <!-- Contact content will be populated by JavaScript -->
                        </div>
                    </div>
                </section>

                <!-- Socials Section -->
                <section id="socials" class="content-section">
                    <div class="section-content">
                        <h2 class="section-title">Connect With Me</h2>
                        <div class="socials-grid">
                            <!-- Social links will be populated by JavaScript -->
                        </div>
                    </div>
                </section>
            </main>
        </div>
    </div>

    <script src="js/main.js"></script>
    <script src="js/terminal.js"></script>
    <script src="js/projects.js"></script>
    <script src="js/blog.js"></script>
</body>
</html>
